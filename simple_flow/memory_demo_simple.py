#!/usr/bin/env python3
"""
简单的CrewAI记忆功能演示

这个演示展示了如何在CrewAI中使用记忆功能来：
1. 记住用户偏好
2. 保存任务执行历史
3. 存储重要实体信息
4. 在多次运行中保持上下文连续性
"""

import os
import random
from content_crew import ContentCrew

def demonstrate_memory_features():
    """演示记忆功能"""
    print("🧠 CrewAI 记忆功能演示")
    print("=" * 50)
    
    print("\n📚 记忆类型说明:")
    print("1. 长期记忆 (Long-term Memory): 存储任务执行历史和质量评分")
    print("2. 短期记忆 (Short-term Memory): 存储当前会话的上下文信息")
    print("3. 实体记忆 (Entity Memory): 存储重要实体和概念")
    
    # 创建带有记忆功能的crew
    print("\n🚀 创建带有记忆功能的ContentCrew...")
    crew = ContentCrew().crew()
    
    # 准备多个主题进行演示
    topics = [
        "人工智能在教育中的应用",
        "可持续发展与绿色技术",
        "远程工作的未来趋势",
        "数字化转型的挑战",
        "智能家居技术发展"
    ]
    
    print(f"\n📝 将依次处理 {len(topics)} 个主题来演示记忆功能...")
    
    results = []
    
    for i, topic in enumerate(topics, 1):
        print(f"\n{'='*60}")
        print(f"🎯 第 {i} 轮: 处理主题 '{topic}'")
        print(f"{'='*60}")
        
        # 准备输入
        inputs = {
            "topic": topic,
            "round": i,
            "previous_topics": topics[:i-1] if i > 1 else []
        }
        
        print(f"📥 输入信息:")
        print(f"  主题: {topic}")
        print(f"  轮次: {i}")
        if inputs["previous_topics"]:
            print(f"  之前的主题: {', '.join(inputs['previous_topics'])}")
        
        # 执行任务
        print(f"\n🔄 执行任务...")
        try:
            result = crew.kickoff(inputs=inputs)
            results.append({
                "topic": topic,
                "content": result.raw,
                "round": i
            })
            
            print(f"✅ 任务完成!")
            print(f"📄 生成内容长度: {len(result.raw)} 字符")
            
            # 显示内容预览
            preview = result.raw[:150] + "..." if len(result.raw) > 150 else result.raw
            print(f"📖 内容预览: {preview}")
            
        except Exception as e:
            print(f"❌ 任务执行失败: {e}")
            continue
        
        # 模拟记忆存储过程
        print(f"\n💾 记忆存储过程:")
        print(f"  ✅ 长期记忆: 保存了任务'{topic}'的执行结果")
        print(f"  ✅ 短期记忆: 更新了当前会话的上下文")
        print(f"  ✅ 实体记忆: 提取并存储了相关实体信息")
        
        if i < len(topics):
            print(f"\n⏳ 准备处理下一个主题...")
    
    # 生成最终报告
    print(f"\n{'='*60}")
    print(f"📊 记忆功能演示总结")
    print(f"{'='*60}")
    
    print(f"\n📈 执行统计:")
    print(f"  总处理主题数: {len(results)}")
    print(f"  成功率: {len(results)}/{len(topics)} ({len(results)/len(topics)*100:.1f}%)")
    
    total_content_length = sum(len(r["content"]) for r in results)
    print(f"  总生成内容: {total_content_length} 字符")
    
    print(f"\n🧠 记忆功能效果:")
    print(f"  • 每次执行都会记住之前的任务结果")
    print(f"  • 可以基于历史信息改进后续任务")
    print(f"  • 实体信息被持久化存储，便于后续引用")
    print(f"  • 用户偏好和上下文得到保持")
    
    print(f"\n📁 生成的记忆文件:")
    memory_files = [
        "./content_crew_memory.db",
        "./content_crew_rag/",
        "./content_crew_entities/"
    ]
    
    for file_path in memory_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  📄 {file_path} ({size} bytes)")
            else:
                print(f"  📁 {file_path} (目录)")
        else:
            print(f"  ❓ {file_path} (未找到)")
    
    return results

def demonstrate_memory_retrieval():
    """演示记忆检索功能"""
    print(f"\n{'='*60}")
    print(f"🔍 记忆检索演示")
    print(f"{'='*60}")
    
    print("\n💡 记忆检索功能说明:")
    print("• 长期记忆: 可以查询历史任务执行情况")
    print("• 短期记忆: 可以搜索当前会话的相关信息")
    print("• 实体记忆: 可以查找特定实体的相关信息")
    
    # 这里可以添加实际的记忆检索代码
    # 由于需要访问crew的内部记忆组件，这里只做演示说明
    
    print(f"\n🔎 模拟记忆查询:")
    queries = [
        "人工智能相关的任务",
        "技术发展趋势",
        "用户偏好信息"
    ]
    
    for query in queries:
        print(f"  查询: '{query}'")
        print(f"    → 长期记忆: 找到 2 个相关任务")
        print(f"    → 短期记忆: 找到 3 个相关上下文")
        print(f"    → 实体记忆: 找到 5 个相关实体")

def cleanup_memory_files():
    """清理记忆文件"""
    import shutil
    
    memory_files = [
        "./content_crew_memory.db",
        "./content_crew_rag/",
        "./content_crew_entities/"
    ]
    
    print(f"\n🧹 清理记忆文件...")
    for file_path in memory_files:
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                print(f"  ✅ 删除文件: {file_path}")
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
                print(f"  ✅ 删除目录: {file_path}")
        except Exception as e:
            print(f"  ⚠️ 无法删除 {file_path}: {e}")

def main():
    """主函数"""
    try:
        # 运行记忆功能演示
        results = demonstrate_memory_features()
        
        # 演示记忆检索
        demonstrate_memory_retrieval()
        
        # 询问是否清理文件
        print(f"\n❓ 是否要清理生成的记忆文件? (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是']:
                cleanup_memory_files()
                print("✨ 演示完成，文件已清理!")
            else:
                print("📁 记忆文件已保留，您可以查看其内容。")
                print("💡 提示: 下次运行时，crew将能够访问这些记忆信息。")
        except KeyboardInterrupt:
            print(f"\n📁 记忆文件已保留。")
            
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("💡 请确保已正确安装CrewAI并配置了必要的环境。")

if __name__ == "__main__":
    main()
