#!/usr/bin/env python3
import random
import re
from pydantic import BaseModel
from crewai.flow.flow import Flow, start, listen, router, or_, and_
from content_crew import ContentCrew

# 定义Flow的状态模型
class ContentState(BaseModel):
    topic: str = ""
    content: str = ""
    content_type: str = ""  # 新增：内容类型（技术型/非技术型）
    content_length: str = ""  # 新增：内容长度（长/短）
    summary: str = ""  # 新增：内容摘要

# 创建Flow类
class ContentFlow(Flow[ContentState]):
    """一个简单的内容创建Flow"""

    @start()
    def generate_topic(self):
        """Flow的起始点 - 生成一个随机主题"""
        print("生成随机主题...")
        topics = [
            "人工智能的未来",
            "太空探索的最新进展",
            "可持续发展与环保",
            "数字化转型的挑战",
            "健康生活方式的重要性"
        ]
        self.state.topic = random.choice(topics)
        print(f"选择的主题: {self.state.topic}")
        return {"topic": self.state.topic}

    @listen(generate_topic)
    def create_content(self, topic_data):
        """监听generate_topic事件，使用Crew创建内容"""
        print(f"正在创建关于'{topic_data['topic']}'的内容...")

        # 使用ContentCrew创建内容
        result = ContentCrew().crew().kickoff(inputs=topic_data)

        # 保存结果到状态
        self.state.content = result.raw
        print("内容创建完成!")
        return self.state.content

    @router(create_content)
    def analyze_content(self):
        """分析内容并决定下一步处理路径"""
        content = self.state.content

        # 分析内容长度
        if len(content.split()) > 150:  # 假设超过150个单词为长内容
            self.state.content_length = "long"
        else:
            self.state.content_length = "short"

        # 分析内容类型（技术型/非技术型）
        tech_keywords = ["人工智能", "数字化", "技术", "算法", "编程", "数据", "科技", "系统"]
        if any(keyword in content for keyword in tech_keywords):
            self.state.content_type = "technical"
        else:
            self.state.content_type = "non_technical"

        print(f"内容分析完成: 长度={self.state.content_length}, 类型={self.state.content_type}")

        # 返回路由路径
        if self.state.content_length == "long" and self.state.content_type == "technical":
            return "long_technical"
        elif self.state.content_length == "long" and self.state.content_type == "non_technical":
            return "long_non_technical"
        elif self.state.content_length == "short" and self.state.content_type == "technical":
            return "short_technical"
        else:
            return "short_non_technical"

    @listen("long_technical")
    def process_long_technical(self):
        """处理长篇技术内容"""
        print("处理长篇技术内容...")
        # 为长篇技术内容添加技术术语表
        glossary = "\n\n## 技术术语表\n\n"
        glossary += "- **AI**: 人工智能，模拟人类智能的计算机系统\n"
        glossary += "- **算法**: 解决问题的一系列明确步骤\n"
        glossary += "- **数据分析**: 检查、清洗、转换数据以发现有用信息的过程\n"

        self.state.content += glossary
        self.state.summary = "这是一篇长篇技术文章，已添加技术术语表以帮助理解。"
        return self.state.content

    @listen("long_non_technical")
    def process_long_non_technical(self):
        """处理长篇非技术内容"""
        print("处理长篇非技术内容...")
        # 为长篇非技术内容添加摘要
        summary = "\n\n## 内容摘要\n\n"
        summary += "这篇文章探讨了" + self.state.topic + "的各个方面，提供了全面的见解和观点。"

        self.state.content += summary
        self.state.summary = "这是一篇长篇非技术文章，已添加内容摘要以便快速了解。"
        return self.state.content

    @listen("short_technical")
    def process_short_technical(self):
        """处理短篇技术内容"""
        print("处理短篇技术内容...")
        # 为短篇技术内容添加进一步阅读建议
        further_reading = "\n\n## 进一步阅读\n\n"
        further_reading += "- 《深入了解" + self.state.topic + "》\n"
        further_reading += "- 《" + self.state.topic + "技术指南》\n"

        self.state.content += further_reading
        self.state.summary = "这是一篇简短的技术文章，已添加进一步阅读建议以深入学习。"
        return self.state.content

    @listen("short_non_technical")
    def process_short_non_technical(self):
        """处理短篇非技术内容"""
        print("处理短篇非技术内容...")
        # 为短篇非技术内容添加相关话题
        related_topics = "\n\n## 相关话题\n\n"
        related_topics += "- " + self.state.topic + "的历史发展\n"
        related_topics += "- " + self.state.topic + "的未来趋势\n"

        self.state.content += related_topics
        self.state.summary = "这是一篇简短的非技术文章，已添加相关话题以扩展阅读范围。"
        return self.state.content

    @listen(or_("long_technical", "long_non_technical", "short_technical", "short_non_technical"))
    def save_content(self, processed_content):
        """保存处理后的内容到文件"""
        print("保存内容到文件...")
        filename = f"{self.state.topic.replace(' ', '_').lower()}.md"

        # 添加处理类型信息到内容开头
        header = f"# {self.state.topic}\n\n"
        header += f"*内容类型: {self.state.content_type}, 内容长度: {self.state.content_length}*\n\n"
        header += f"**摘要**: {self.state.summary}\n\n"
        header += "---\n\n"

        final_content = header + processed_content

        with open(filename, "w") as f:
            f.write(final_content)

        print(f"内容已保存到文件: {filename}")
        return {"filename": filename, "content": final_content}

def run():
    """运行Flow"""
    flow = ContentFlow()
    result = flow.kickoff()
    print("\n最终结果:", result)

def plot():
    """生成Flow的可视化图表"""
    flow = ContentFlow()
    flow.plot()

if __name__ == "__main__":
    run()
