#!/usr/bin/env python3
"""
简单的记忆功能测试脚本
"""

def test_memory_imports():
    """测试记忆相关的导入"""
    print("🧠 测试CrewAI记忆功能导入...")
    
    try:
        from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory
        print("✅ 记忆模块导入成功")
        
        from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
        print("✅ 长期记忆存储导入成功")
        
        from crewai.memory.storage.rag_storage import RAGStorage
        print("✅ RAG存储导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_memory_creation():
    """测试记忆对象创建"""
    print("\n🔧 测试记忆对象创建...")
    
    try:
        from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory
        from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
        from crewai.memory.storage.rag_storage import RAGStorage
        
        # 测试长期记忆
        ltm = LongTermMemory(
            storage=LTMSQLiteStorage(db_path="./test_memory.db")
        )
        print("✅ 长期记忆对象创建成功")
        
        # 测试短期记忆
        stm = ShortTermMemory(
            storage=RAGStorage(
                type="short_term",
                path="./test_rag/"
            )
        )
        print("✅ 短期记忆对象创建成功")
        
        # 测试实体记忆
        em = EntityMemory(
            storage=RAGStorage(
                type="entities",
                path="./test_entities/"
            )
        )
        print("✅ 实体记忆对象创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 记忆对象创建失败: {e}")
        return False

def show_memory_info():
    """显示记忆功能信息"""
    print("\n📚 CrewAI 记忆功能说明:")
    print("=" * 50)
    
    print("\n🧠 记忆类型:")
    print("1. 长期记忆 (Long-term Memory)")
    print("   - 存储任务执行历史和质量评分")
    print("   - 使用SQLite数据库持久化存储")
    print("   - 跨会话保持数据")
    
    print("\n2. 短期记忆 (Short-term Memory)")
    print("   - 存储当前会话的上下文信息")
    print("   - 使用RAG (检索增强生成) 技术")
    print("   - 支持语义搜索")
    
    print("\n3. 实体记忆 (Entity Memory)")
    print("   - 存储重要实体和关系信息")
    print("   - 使用向量数据库存储")
    print("   - 支持实体关系查询")
    
    print("\n💡 使用场景:")
    print("• 个性化AI助手")
    print("• 智能客服系统")
    print("• 内容创作助手")
    print("• 知识管理系统")

def main():
    """主函数"""
    print("🚀 CrewAI 记忆功能测试")
    print("=" * 40)
    
    # 测试导入
    if not test_memory_imports():
        print("\n❌ 记忆功能不可用，请检查CrewAI安装")
        return
    
    # 测试对象创建
    if not test_memory_creation():
        print("\n❌ 记忆对象创建失败")
        return
    
    # 显示信息
    show_memory_info()
    
    print("\n✨ 记忆功能测试完成!")
    print("\n📝 下一步:")
    print("1. 运行 memory_demo_simple.py 查看完整演示")
    print("2. 运行 practical_memory_demo.py 查看实用场景")
    print("3. 查看 MEMORY_DEMO_README.md 了解详细说明")

if __name__ == "__main__":
    main()
