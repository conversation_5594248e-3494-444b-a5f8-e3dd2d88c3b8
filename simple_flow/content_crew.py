from typing import List
from crewai import Agent, Crew, Task, Process
from crewai.project import CrewBase, agent, task, crew
from crewai.agents.agent_builder.base_agent import BaseAgent

@CrewBase
class ContentCrew:
    """Content creation crew"""
    
    agents: List[BaseAgent]
    tasks: List[Task]
    
    # 配置文件路径
    agents_config = "config/agents.yaml"
    tasks_config = "config/tasks.yaml"
    
    @agent
    def writer(self) -> Agent:
        return Agent(
            config=self.agents_config['writer'],
            verbose=True
        )
    
    @task
    def write_content(self) -> Task:
        return Task(
            config=self.tasks_config['write_content']
        )
    
    @crew
    def crew(self) -> Crew:
        """Creates the content creation crew"""
        return Crew(
            agents=self.agents,  # 由@agent装饰器自动创建
            tasks=self.tasks,    # 由@task装饰器自动创建
            process=Process.sequential,
            verbose=True,
        )
