#!/usr/bin/env python3
"""
实用的CrewAI记忆功能演示

这个演示展示了记忆功能在实际应用场景中的使用：
1. 个人助手场景 - 记住用户偏好和历史对话
2. 内容创作场景 - 记住写作风格和主题偏好
3. 客户服务场景 - 记住客户信息和服务历史
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any
from pydantic import BaseModel
from crewai import Agent, Crew, Task, Process
from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
from crewai.memory.storage.rag_storage import RAGStorage

class UserProfile(BaseModel):
    """用户档案模型"""
    name: str = ""
    preferences: Dict[str, Any] = {}
    interaction_history: List[str] = []
    entities_of_interest: List[str] = []

class MemoryEnabledAssistant:
    """带有记忆功能的AI助手"""
    
    def __init__(self, user_name: str = "用户"):
        self.user_name = user_name
        self.user_profile = UserProfile(name=user_name)
        self.crew = self._create_memory_crew()
        
    def _create_memory_crew(self) -> Crew:
        """创建带有记忆功能的crew"""
        
        # 创建记忆感知的助手agent
        memory_assistant = Agent(
            role="Memory-Enhanced Personal Assistant",
            goal=f"为{self.user_name}提供个性化的智能助手服务，记住用户偏好和历史交互",
            backstory=f"""你是{self.user_name}的专属AI助手。你能够记住用户的偏好、
            历史对话、重要信息和个人习惯。你会根据这些记忆信息提供更加个性化和
            贴心的服务。你总是友好、专业，并且善于从过往交互中学习。""",
            verbose=True,
            allow_delegation=False
        )
        
        # 创建记忆分析agent
        memory_analyzer = Agent(
            role="Memory Analyst",
            goal="分析用户交互，提取重要信息并更新用户档案",
            backstory="""你是一个专业的记忆分析师，擅长从对话中提取关键信息，
            识别用户偏好模式，并维护准确的用户档案。你能够识别重要实体、
            情感倾向和行为模式。""",
            verbose=True,
            allow_delegation=False
        )
        
        # 创建任务
        assist_task = Task(
            description="""基于用户的请求和历史记忆信息，提供个性化的帮助。
            
            用户请求: {user_request}
            用户档案: {user_profile}
            
            请考虑用户的历史偏好和之前的交互，提供贴心和个性化的回应。
            如果这是一个重复的请求，请参考之前的处理方式。
            如果发现新的偏好或模式，请在回应中体现出来。""",
            expected_output="个性化的助手回应，体现对用户历史和偏好的理解",
            agent=memory_assistant
        )
        
        analyze_task = Task(
            description="""分析本次交互，提取以下信息：
            
            用户请求: {user_request}
            助手回应: {assistant_response}
            
            请提取：
            1. 新发现的用户偏好
            2. 重要实体（人名、地点、概念等）
            3. 情感倾向和满意度
            4. 可以改进的地方
            
            以JSON格式输出分析结果。""",
            expected_output="JSON格式的交互分析结果",
            agent=memory_analyzer
        )
        
        return Crew(
            agents=[memory_assistant, memory_analyzer],
            tasks=[assist_task, analyze_task],
            process=Process.sequential,
            verbose=True,
            # 启用记忆功能
            memory=True,
            # 配置长期记忆
            long_term_memory=LongTermMemory(
                storage=LTMSQLiteStorage(db_path=f"./assistant_{self.user_name}_memory.db")
            ),
            # 配置短期记忆
            short_term_memory=ShortTermMemory(
                storage=RAGStorage(
                    type="short_term",
                    path=f"./assistant_{self.user_name}_rag/"
                )
            ),
            # 配置实体记忆
            entity_memory=EntityMemory(
                storage=RAGStorage(
                    type="entities",
                    path=f"./assistant_{self.user_name}_entities/"
                )
            )
        )
    
    def interact(self, user_request: str) -> Dict[str, Any]:
        """与用户交互"""
        print(f"\n👤 {self.user_name}: {user_request}")
        
        # 准备输入数据
        inputs = {
            "user_request": user_request,
            "user_profile": self.user_profile.model_dump(),
            "timestamp": datetime.now().isoformat()
        }
        
        # 执行crew任务
        result = self.crew.kickoff(inputs=inputs)
        
        # 解析结果
        tasks_output = result.tasks_output
        assistant_response = tasks_output[0].raw if len(tasks_output) > 0 else "抱歉，我无法处理您的请求。"
        analysis_result = tasks_output[1].raw if len(tasks_output) > 1 else "{}"
        
        print(f"\n🤖 助手: {assistant_response}")
        
        # 更新用户档案
        self._update_user_profile(user_request, assistant_response, analysis_result)
        
        return {
            "user_request": user_request,
            "assistant_response": assistant_response,
            "analysis": analysis_result,
            "timestamp": inputs["timestamp"]
        }
    
    def _update_user_profile(self, request: str, response: str, analysis: str):
        """更新用户档案"""
        try:
            # 尝试解析分析结果
            analysis_data = json.loads(analysis) if analysis.startswith('{') else {}
            
            # 更新交互历史
            interaction = f"{datetime.now().strftime('%Y-%m-%d %H:%M')} - {request[:50]}..."
            self.user_profile.interaction_history.append(interaction)
            
            # 保持历史记录在合理长度
            if len(self.user_profile.interaction_history) > 10:
                self.user_profile.interaction_history = self.user_profile.interaction_history[-10:]
            
            # 更新偏好（如果分析中有新发现）
            if "preferences" in analysis_data:
                self.user_profile.preferences.update(analysis_data["preferences"])
            
            # 更新感兴趣的实体
            if "entities" in analysis_data:
                new_entities = analysis_data["entities"]
                for entity in new_entities:
                    if entity not in self.user_profile.entities_of_interest:
                        self.user_profile.entities_of_interest.append(entity)
            
        except Exception as e:
            print(f"⚠️ 更新用户档案时出错: {e}")
    
    def show_memory_summary(self):
        """显示记忆摘要"""
        print(f"\n{'='*60}")
        print(f"🧠 {self.user_name} 的记忆档案")
        print(f"{'='*60}")
        
        print(f"\n👤 用户信息:")
        print(f"  姓名: {self.user_profile.name}")
        print(f"  交互次数: {len(self.user_profile.interaction_history)}")
        
        print(f"\n⚙️ 用户偏好:")
        if self.user_profile.preferences:
            for key, value in self.user_profile.preferences.items():
                print(f"  {key}: {value}")
        else:
            print("  暂无记录的偏好")
        
        print(f"\n🏷️ 感兴趣的实体:")
        if self.user_profile.entities_of_interest:
            for entity in self.user_profile.entities_of_interest:
                print(f"  • {entity}")
        else:
            print("  暂无记录的实体")
        
        print(f"\n📝 最近交互:")
        for interaction in self.user_profile.interaction_history[-5:]:
            print(f"  • {interaction}")
        
        # 显示记忆文件信息
        print(f"\n📁 记忆存储:")
        memory_files = [
            f"./assistant_{self.user_name}_memory.db",
            f"./assistant_{self.user_name}_rag/",
            f"./assistant_{self.user_name}_entities/"
        ]
        
        for file_path in memory_files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file_path} ({size} bytes)")
                else:
                    print(f"  ✅ {file_path} (目录)")
            else:
                print(f"  ❌ {file_path} (未找到)")

def run_personal_assistant_demo():
    """运行个人助手演示"""
    print("🎭 个人助手记忆功能演示")
    print("=" * 50)
    
    # 创建助手
    assistant = MemoryEnabledAssistant("张三")
    
    # 模拟多轮对话
    conversations = [
        "你好，我想了解一下人工智能的最新发展",
        "我对机器学习特别感兴趣，能推荐一些学习资源吗？",
        "我比较喜欢实践性强的教程，有什么推荐吗？",
        "上次你推荐的机器学习资源很好，还有类似的吗？",
        "我想学习深度学习，基于我之前的兴趣，你有什么建议？"
    ]
    
    print(f"\n🎬 开始模拟对话...")
    
    for i, request in enumerate(conversations, 1):
        print(f"\n{'='*40} 第 {i} 轮对话 {'='*40}")
        result = assistant.interact(request)
        
        if i < len(conversations):
            print(f"\n⏳ 等待下一轮对话...")
    
    # 显示记忆摘要
    assistant.show_memory_summary()
    
    return assistant

def run_content_creator_demo():
    """运行内容创作者演示"""
    print(f"\n{'='*60}")
    print("✍️ 内容创作者记忆功能演示")
    print("=" * 60)
    
    # 创建内容创作助手
    creator_assistant = MemoryEnabledAssistant("内容创作者")
    
    # 模拟内容创作场景
    content_requests = [
        "帮我写一篇关于可持续发展的文章，要求专业但易懂",
        "我需要一篇技术博客，主题是云计算，风格要偏向实用性",
        "写一篇关于远程工作的文章，保持之前的专业风格",
        "基于我之前的写作偏好，帮我写一篇关于数字化转型的文章"
    ]
    
    for i, request in enumerate(content_requests, 1):
        print(f"\n{'='*30} 创作请求 {i} {'='*30}")
        creator_assistant.interact(request)
    
    creator_assistant.show_memory_summary()
    
    return creator_assistant

def cleanup_demo_files():
    """清理演示文件"""
    import shutil
    import glob
    
    print(f"\n🧹 清理演示文件...")
    
    # 查找所有演示生成的文件
    patterns = [
        "./assistant_*_memory.db",
        "./assistant_*_rag/",
        "./assistant_*_entities/"
    ]
    
    files_removed = 0
    for pattern in patterns:
        for file_path in glob.glob(pattern):
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"  ✅ 删除文件: {file_path}")
                    files_removed += 1
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"  ✅ 删除目录: {file_path}")
                    files_removed += 1
            except Exception as e:
                print(f"  ⚠️ 无法删除 {file_path}: {e}")
    
    print(f"  📊 总共清理了 {files_removed} 个文件/目录")

def main():
    """主函数"""
    print("🚀 CrewAI 实用记忆功能演示")
    print("=" * 60)
    
    try:
        # 运行个人助手演示
        assistant1 = run_personal_assistant_demo()
        
        # 运行内容创作者演示
        assistant2 = run_content_creator_demo()
        
        # 显示总结
        print(f"\n{'='*60}")
        print("📊 演示总结")
        print("=" * 60)
        
        print(f"\n✅ 成功演示了以下记忆功能:")
        print(f"  • 个性化交互 - 基于用户历史调整回应")
        print(f"  • 偏好学习 - 从对话中学习用户偏好")
        print(f"  • 上下文连续性 - 跨对话保持上下文")
        print(f"  • 实体记忆 - 记住重要概念和实体")
        print(f"  • 档案维护 - 自动更新用户档案")
        
        print(f"\n💡 记忆功能的实际价值:")
        print(f"  • 提供更个性化的用户体验")
        print(f"  • 减少重复信息收集")
        print(f"  • 提高服务质量和效率")
        print(f"  • 建立长期的用户关系")
        
        # 询问是否清理文件
        print(f"\n❓ 是否要清理生成的记忆文件? (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是']:
                cleanup_demo_files()
                print("✨ 演示完成，文件已清理!")
            else:
                print("📁 记忆文件已保留。")
                print("💡 您可以继续与助手交互，它们会记住之前的对话。")
        except KeyboardInterrupt:
            print(f"\n📁 记忆文件已保留。")
            
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("💡 请确保已正确安装CrewAI并配置了必要的环境。")

if __name__ == "__main__":
    main()
