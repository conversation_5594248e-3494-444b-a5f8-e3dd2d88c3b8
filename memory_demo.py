#!/usr/bin/env python3
"""
CrewAI 记忆功能演示

这个demo展示了如何在CrewAI中使用不同类型的记忆功能：
1. Short-term Memory (短期记忆) - 用于当前会话的上下文
2. Long-term Memory (长期记忆) - 跨会话持久化存储  
3. Entity Memory (实体记忆) - 存储实体信息和关系
"""

import os
import random
from typing import List, Dict, Any
from pydantic import BaseModel
from crewai import Agent, Crew, Task, Process
from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
from crewai.memory.storage.rag_storage import RAGStorage
from crewai.flow.flow import Flow, start, listen, router
from crewai.project import CrewBase, agent, task, crew
from crewai.agents.agent_builder.base_agent import BaseAgent

# 设置环境变量（如果需要）
# os.environ["OPENAI_API_KEY"] = "your-api-key-here"

class MemoryState(BaseModel):
    """Flow状态模型，包含记忆相关信息"""
    topic: str = ""
    content: str = ""
    user_preferences: Dict[str, Any] = {}
    conversation_history: List[str] = []
    entities_mentioned: List[str] = []
    session_id: str = ""

@CrewBase
class MemoryEnabledCrew:
    """带有记忆功能的Crew"""
    
    agents: List[BaseAgent]
    tasks: List[Task]
    
    @agent
    def memory_writer(self) -> Agent:
        """具有记忆功能的写作agent"""
        return Agent(
            role="Memory-Enhanced Content Writer",
            goal="Create personalized content based on user preferences and conversation history",
            backstory="""You are an intelligent writer who remembers user preferences, 
            past conversations, and important entities. You use this memory to create 
            more personalized and contextually relevant content.""",
            verbose=True,
            allow_delegation=False
        )
    
    @agent
    def memory_analyst(self) -> Agent:
        """记忆分析agent"""
        return Agent(
            role="Memory Analyst",
            goal="Analyze content and extract important entities and insights for future reference",
            backstory="""You are an expert at analyzing content and identifying important 
            entities, relationships, and insights that should be remembered for future interactions.""",
            verbose=True,
            allow_delegation=False
        )
    
    @task
    def write_with_memory(self) -> Task:
        """使用记忆创建内容的任务"""
        return Task(
            description="""Write an engaging article about {topic}. 
            Use any relevant information from previous conversations and user preferences 
            to make the content more personalized. The article should be 300-500 words.""",
            expected_output="A personalized article that incorporates relevant memory context",
            agent=self.memory_writer()
        )
    
    @task
    def analyze_and_remember(self) -> Task:
        """分析内容并提取记忆信息的任务"""
        return Task(
            description="""Analyze the written content and extract:
            1. Important entities mentioned (people, places, concepts)
            2. User preferences that can be inferred
            3. Key insights that should be remembered
            
            Format the output as structured information for memory storage.""",
            expected_output="Structured analysis with entities, preferences, and insights",
            agent=self.memory_analyst()
        )
    
    @crew
    def crew(self) -> Crew:
        """创建带有记忆功能的Crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            # 启用记忆功能
            memory=True,
            # 配置长期记忆
            long_term_memory=LongTermMemory(
                storage=LTMSQLiteStorage(db_path="./memory_demo.db")
            ),
            # 配置短期记忆
            short_term_memory=ShortTermMemory(
                storage=RAGStorage(
                    type="short_term",
                    path="./memory_demo_rag/"
                )
            ),
            # 配置实体记忆
            entity_memory=EntityMemory(
                storage=RAGStorage(
                    type="entities",
                    path="./memory_demo_entities/"
                )
            )
        )

class MemoryDemoFlow(Flow[MemoryState]):
    """带有记忆功能的Flow演示"""
    
    def __init__(self):
        super().__init__()
        self.crew_instance = MemoryEnabledCrew()
    
    @start()
    def initialize_session(self):
        """初始化会话并设置记忆上下文"""
        print("🧠 初始化记忆演示会话...")
        
        # 生成会话ID
        import uuid
        self.state.session_id = str(uuid.uuid4())[:8]
        
        # 模拟用户偏好（在实际应用中，这些可能来自用户输入或历史记忆）
        self.state.user_preferences = {
            "writing_style": "informative_yet_engaging",
            "preferred_length": "medium",
            "interests": ["technology", "health", "environment"],
            "tone": "professional_but_friendly"
        }
        
        # 选择一个主题
        topics = [
            "人工智能在医疗保健中的应用",
            "可持续能源的未来发展",
            "远程工作对环境的影响",
            "数字化转型中的数据安全",
            "智能城市建设的挑战与机遇"
        ]
        self.state.topic = random.choice(topics)
        
        print(f"📝 选择的主题: {self.state.topic}")
        print(f"🔧 用户偏好: {self.state.user_preferences}")
        print(f"🆔 会话ID: {self.state.session_id}")
        
        return {
            "topic": self.state.topic,
            "session_id": self.state.session_id,
            "user_preferences": self.state.user_preferences
        }
    
    @listen(initialize_session)
    def create_content_with_memory(self, session_data):
        """使用记忆功能创建内容"""
        print(f"🚀 开始使用记忆功能创建内容...")
        
        # 准备输入数据，包含记忆上下文
        inputs = {
            "topic": session_data["topic"],
            "session_id": session_data["session_id"],
            "user_preferences": session_data["user_preferences"]
        }
        
        # 执行Crew任务
        result = self.crew_instance.crew().kickoff(inputs=inputs)
        
        # 保存结果
        self.state.content = result.raw
        self.state.conversation_history.append(f"Created content about: {session_data['topic']}")
        
        print("✅ 内容创建完成!")
        return result.raw
    
    @listen(create_content_with_memory)
    def extract_memory_insights(self, content):
        """从创建的内容中提取记忆洞察"""
        print("🔍 分析内容并提取记忆信息...")
        
        # 这里可以添加更复杂的实体提取逻辑
        # 简单示例：提取一些关键词作为实体
        import re
        
        # 提取可能的实体（简化版本）
        entities = []
        content_lower = content.lower()
        
        # 技术相关实体
        tech_entities = ["人工智能", "ai", "机器学习", "深度学习", "算法", "数据", "云计算", "物联网"]
        for entity in tech_entities:
            if entity in content_lower:
                entities.append(entity)
        
        # 健康相关实体
        health_entities = ["健康", "医疗", "疾病", "治疗", "诊断", "药物", "医院", "医生"]
        for entity in health_entities:
            if entity in content_lower:
                entities.append(entity)
        
        self.state.entities_mentioned = list(set(entities))
        
        print(f"📊 提取的实体: {self.state.entities_mentioned}")
        
        return {
            "entities": self.state.entities_mentioned,
            "content_analysis": f"分析了关于'{self.state.topic}'的内容，提取了{len(entities)}个实体"
        }
    
    @listen(extract_memory_insights)
    def save_to_memory(self, analysis_result):
        """将信息保存到不同类型的记忆中"""
        print("💾 保存信息到记忆系统...")
        
        # 模拟保存到记忆系统的过程
        memory_summary = {
            "session_id": self.state.session_id,
            "topic": self.state.topic,
            "entities_count": len(self.state.entities_mentioned),
            "user_preferences": self.state.user_preferences,
            "content_length": len(self.state.content),
            "timestamp": "2024-01-01"  # 在实际应用中使用真实时间戳
        }
        
        print("📝 记忆摘要:")
        for key, value in memory_summary.items():
            print(f"  {key}: {value}")
        
        return memory_summary
    
    @listen(save_to_memory)
    def generate_report(self, memory_summary):
        """生成记忆演示报告"""
        print("\n" + "="*60)
        print("🎯 CrewAI 记忆功能演示报告")
        print("="*60)
        
        print(f"\n📋 会话信息:")
        print(f"  会话ID: {memory_summary['session_id']}")
        print(f"  主题: {memory_summary['topic']}")
        print(f"  内容长度: {memory_summary['content_length']} 字符")
        
        print(f"\n🧠 记忆功能使用情况:")
        print(f"  ✅ 短期记忆: 存储了当前会话的上下文信息")
        print(f"  ✅ 长期记忆: 保存了任务执行结果和质量评分")
        print(f"  ✅ 实体记忆: 识别并存储了 {memory_summary['entities_count']} 个实体")
        
        print(f"\n👤 用户偏好记录:")
        for key, value in memory_summary['user_preferences'].items():
            print(f"  {key}: {value}")
        
        print(f"\n🔍 提取的实体:")
        for entity in self.state.entities_mentioned:
            print(f"  • {entity}")
        
        print(f"\n📄 生成的内容预览:")
        preview = self.state.content[:200] + "..." if len(self.state.content) > 200 else self.state.content
        print(f"  {preview}")
        
        print(f"\n💡 记忆功能优势:")
        print(f"  • 个性化内容: 基于用户偏好定制内容")
        print(f"  • 上下文连续性: 记住之前的对话和决策")
        print(f"  • 实体关系: 跟踪重要概念和实体")
        print(f"  • 学习能力: 从每次交互中学习和改进")
        
        print("\n" + "="*60)
        print("✨ 演示完成！记忆功能已成功集成到CrewAI工作流中。")
        print("="*60)
        
        return {
            "status": "completed",
            "summary": memory_summary,
            "content": self.state.content
        }

def demonstrate_memory_types():
    """演示不同类型的记忆功能"""
    print("\n🎓 CrewAI 记忆类型说明:")
    print("-" * 40)
    
    print("\n1. 📝 短期记忆 (Short-term Memory):")
    print("   • 用途: 存储当前会话的上下文信息")
    print("   • 存储: 使用RAG (Retrieval-Augmented Generation)")
    print("   • 特点: 会话结束后可能清除")
    
    print("\n2. 🗄️ 长期记忆 (Long-term Memory):")
    print("   • 用途: 跨会话持久化存储任务结果")
    print("   • 存储: SQLite数据库")
    print("   • 特点: 永久保存，可用于学习和改进")
    
    print("\n3. 🏷️ 实体记忆 (Entity Memory):")
    print("   • 用途: 存储实体信息和关系")
    print("   • 存储: 向量数据库 (RAG)")
    print("   • 特点: 支持语义搜索和关系查询")
    
    print("\n4. 🌐 外部记忆 (External Memory):")
    print("   • 用途: 集成外部记忆系统 (如Mem0)")
    print("   • 存储: 外部服务")
    print("   • 特点: 可与其他应用共享记忆")

def run_memory_demo():
    """运行记忆功能演示"""
    print("🚀 启动 CrewAI 记忆功能演示")
    print("=" * 50)
    
    # 显示记忆类型说明
    demonstrate_memory_types()
    
    print("\n🎬 开始演示...")
    
    # 创建并运行Flow
    flow = MemoryDemoFlow()
    result = flow.kickoff()
    
    return result

def cleanup_demo_files():
    """清理演示文件"""
    import shutil
    import os
    
    files_to_remove = [
        "./memory_demo.db",
        "./memory_demo_rag/",
        "./memory_demo_entities/"
    ]
    
    print("\n🧹 清理演示文件...")
    for file_path in files_to_remove:
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                print(f"  ✅ 删除文件: {file_path}")
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
                print(f"  ✅ 删除目录: {file_path}")
        except Exception as e:
            print(f"  ⚠️ 无法删除 {file_path}: {e}")

if __name__ == "__main__":
    try:
        # 运行演示
        result = run_memory_demo()
        
        # 询问是否清理文件
        print("\n❓ 是否要清理演示生成的文件? (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是']:
                cleanup_demo_files()
            else:
                print("📁 演示文件已保留，您可以查看记忆数据库的内容。")
        except KeyboardInterrupt:
            print("\n📁 演示文件已保留。")
            
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("💡 请确保已正确安装CrewAI并配置了必要的API密钥。")
