#!/usr/bin/env python3
import random
from pydantic import BaseModel
from crewai.flow.flow import Flow, start, listen, router, or_, and_
from crewai import Agent, Task, Crew, Process

# 定义Flow的状态模型
class ProjectState(BaseModel):
    project_name: str = ""
    requirements: list = []
    design_doc: str = ""
    code: str = ""
    test_results: str = ""
    review_feedback: str = ""
    final_report: str = ""
    status: str = "not_started"  # not_started, in_progress, completed, failed

# 创建分层执行的Flow类
class HierarchicalProjectFlow(Flow[ProjectState]):
    """一个分层执行的项目开发Flow"""

    @start()
    def initialize_project(self):
        """Flow的起始点 - 初始化项目"""
        print("初始化项目...")
        project_names = [
            "智能助手开发项目",
            "数据分析平台",
            "自动化测试框架",
            "知识图谱构建系统",
            "多模态学习应用"
        ]
        self.state.project_name = random.choice(project_names)
        self.state.status = "in_progress"
        print(f"选择的项目: {self.state.project_name}")
        return {"project_name": self.state.project_name}

    @listen(initialize_project)
    def gather_requirements(self, project_data):
        """收集项目需求"""
        print(f"正在收集'{project_data['project_name']}'的需求...")

        # 创建需求收集Agent
        requirements_agent = Agent(
            role="需求分析师",
            goal="收集并分析项目需求",
            backstory="你是一位经验丰富的需求分析师，擅长理解客户需求并转化为明确的技术规格。"
        )

        # 创建需求收集任务
        requirements_task = Task(
            description=f"分析{self.state.project_name}项目，列出5个关键功能需求和3个非功能性需求。",
            expected_output="一份详细的需求列表，包含功能性和非功能性需求。",
            agent=requirements_agent,
        )

        # 创建Crew并执行任务
        requirements_crew = Crew(
            agents=[requirements_agent],
            tasks=[requirements_task],
            verbose=True
        )

        result = requirements_crew.kickoff()

        # 解析需求结果
        self.state.requirements = result.raw.split("\n")
        print("需求收集完成!")
        return self.state.requirements

    @router(gather_requirements)
    def analyze_project_complexity(self):
        """分析项目复杂度并决定下一步处理路径"""
        requirements = self.state.requirements

        # 分析项目复杂度
        complexity_keywords = ["复杂", "集成", "实时", "分布式", "高并发", "安全", "大规模"]
        complexity_score = sum(1 for req in requirements for keyword in complexity_keywords if keyword in req)

        print(f"项目复杂度分析完成: 复杂度得分={complexity_score}")

        # 根据复杂度返回路由路径
        if complexity_score >= 3:
            return "complex_project"
        else:
            return "simple_project"

    @listen("complex_project")
    def handle_complex_project(self):
        """处理复杂项目的设计和开发"""
        print("处理复杂项目...")

        # 创建分层的Crew结构
        architect = Agent(
            role="系统架构师",
            goal="设计高效且可扩展的系统架构",
            backstory="你是一位资深系统架构师，擅长设计复杂系统的架构。"
        )

        senior_developer = Agent(
            role="高级开发工程师",
            goal="实现核心功能模块",
            backstory="你是一位经验丰富的高级开发工程师，擅长处理复杂的编程任务。"
        )

        tester = Agent(
            role="测试工程师",
            goal="确保系统质量",
            backstory="你是一位细致的测试工程师，擅长发现系统中的缺陷。"
        )

        # 创建项目经理作为管理者
        project_manager = Agent(
            role="项目经理",
            goal="协调团队工作并确保项目成功交付",
            backstory="你是一位经验丰富的项目经理，擅长管理复杂项目和团队协作。",
            allow_delegation=True  # 允许任务委派
        )

        # 创建设计任务
        design_task = Task(
            description=f"为{self.state.project_name}设计系统架构，考虑所有需求: {self.state.requirements}",
            expected_output="一份详细的系统设计文档，包含架构图和关键组件说明。",
            agent=architect,
        )

        # 创建开发任务
        development_task = Task(
            description="根据系统设计文档实现核心功能模块的代码框架。",
            expected_output="核心功能模块的代码实现和说明文档。",
            agent=senior_developer,
        )

        # 创建测试任务
        testing_task = Task(
            description="设计测试用例并对实现的功能进行测试。",
            expected_output="测试报告，包含测试用例、测试结果和发现的问题。",
            agent=tester,
        )

        # 创建分层执行的Crew
        complex_crew = Crew(
            agents=[architect, senior_developer, tester],
            tasks=[design_task, development_task, testing_task],
            process=Process.hierarchical,  # 使用分层处理流程
            manager_agent=project_manager,  # 指定项目经理作为管理者
            verbose=True
        )

        result = complex_crew.kickoff()

        # 保存结果
        self.state.design_doc = "复杂项目设计文档: " + result.raw[:200] + "..."
        self.state.code = "复杂项目代码: " + result.raw[200:400] + "..."
        self.state.test_results = "复杂项目测试结果: " + result.raw[400:600] + "..."

        return {
            "design": self.state.design_doc,
            "code": self.state.code,
            "test_results": self.state.test_results
        }

    @listen("simple_project")
    def handle_simple_project(self):
        """处理简单项目的设计和开发"""
        print("处理简单项目...")

        # 创建全栈开发者
        fullstack_developer = Agent(
            role="全栈开发工程师",
            goal="设计并实现完整的系统",
            backstory="你是一位全能的全栈开发工程师，能够独立完成项目的设计和开发。"
        )

        # 创建简单项目任务
        simple_task = Task(
            description=f"为{self.state.project_name}设计并实现一个简单的原型，满足以下需求: {self.state.requirements}",
            expected_output="一份简单的设计文档和原型代码实现。",
            agent=fullstack_developer,
        )

        # 创建简单Crew
        simple_crew = Crew(
            agents=[fullstack_developer],
            tasks=[simple_task],
            verbose=True
        )

        result = simple_crew.kickoff()

        # 保存结果
        self.state.design_doc = "简单项目设计文档: " + result.raw[:200] + "..."
        self.state.code = "简单项目代码: " + result.raw[200:400] + "..."

        return {
            "design": self.state.design_doc,
            "code": self.state.code
        }

    @listen(or_("handle_complex_project", "handle_simple_project"))
    def review_project(self, project_output):
        """审查项目成果"""
        print("审查项目成果...")

        # 创建审查Agent
        reviewer = Agent(
            role="质量审查专家",
            goal="全面审查项目成果并提供改进建议",
            backstory="你是一位严格的质量审查专家，擅长发现项目中的问题并提供建设性的反馈。"
        )

        # 创建审查任务
        review_task = Task(
            description=f"审查{self.state.project_name}项目的成果，包括设计文档和代码实现，提供详细的反馈和改进建议。",
            expected_output="一份详细的审查报告，包含优点、问题和改进建议。",
            agent=reviewer,
        )

        # 创建审查Crew
        review_crew = Crew(
            agents=[reviewer],
            tasks=[review_task],
            verbose=True
        )

        result = review_crew.kickoff()

        # 保存审查结果
        self.state.review_feedback = result.raw
        print("项目审查完成!")
        return self.state.review_feedback

    @listen(review_project)
    def generate_final_report(self, review_feedback):
        """生成最终项目报告"""
        print("生成最终项目报告...")

        # 创建报告生成Agent
        report_agent = Agent(
            role="技术文档专家",
            goal="生成全面的项目总结报告",
            backstory="你是一位专业的技术文档专家，擅长将复杂的技术信息转化为清晰的文档。"
        )

        # 创建报告任务
        report_task = Task(
            description=f"为{self.state.project_name}项目生成最终报告，总结项目需求、设计、实现和审查结果。",
            expected_output="一份全面的项目总结报告，包含项目概述、成果和后续建议。",
            agent=report_agent,
        )

        # 创建报告Crew
        report_crew = Crew(
            agents=[report_agent],
            tasks=[report_task],
            verbose=True
        )

        result = report_crew.kickoff()

        # 保存最终报告
        self.state.final_report = result.raw
        self.state.status = "completed"
        print("最终报告生成完成!")

        # 保存项目报告到文件
        filename = f"{self.state.project_name.replace(' ', '_').lower()}_report.md"
        with open(filename, "w") as f:
            f.write(f"# {self.state.project_name} 项目报告\n\n")
            f.write(f"## 项目状态: {self.state.status}\n\n")
            f.write("## 项目需求\n\n")
            for req in self.state.requirements:
                f.write(f"- {req}\n")
            f.write("\n## 项目设计\n\n")
            f.write(self.state.design_doc + "\n\n")
            f.write("## 项目实现\n\n")
            f.write(self.state.code + "\n\n")
            if hasattr(self.state, "test_results") and self.state.test_results:
                f.write("## 测试结果\n\n")
                f.write(self.state.test_results + "\n\n")
            f.write("## 审查反馈\n\n")
            f.write(self.state.review_feedback + "\n\n")
            f.write("## 最终报告\n\n")
            f.write(self.state.final_report)

        print(f"项目报告已保存到文件: {filename}")
        return {"filename": filename, "final_report": self.state.final_report}

def run():
    """运行分层执行Flow"""
    flow = HierarchicalProjectFlow()
    result = flow.kickoff()
    print("\n最终结果:", result)

def plot():
    """生成Flow的可视化图表"""
    flow = HierarchicalProjectFlow()
    flow.plot()

if __name__ == "__main__":
    run()
