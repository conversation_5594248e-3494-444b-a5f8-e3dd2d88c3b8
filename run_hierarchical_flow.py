#!/usr/bin/env python3
"""
运行分层执行Flow的示例脚本
"""

from hierarchical_flow.main import run, plot

if __name__ == "__main__":
    print("=" * 50)
    print("开始执行分层Flow...")
    print("=" * 50)
    
    # 运行分层执行Flow
    run()
    
    print("\n" + "=" * 50)
    print("生成Flow可视化图表...")
    print("=" * 50)
    
    # 生成Flow的可视化图表
    plot()
    
    print("\n" + "=" * 50)
    print("执行完成!")
    print("=" * 50)
