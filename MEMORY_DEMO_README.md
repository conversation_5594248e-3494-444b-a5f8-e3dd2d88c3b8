# CrewAI 记忆功能演示

这个项目展示了如何在CrewAI框架中集成和使用记忆功能，让AI agents能够记住过去的交互、学习用户偏好，并在多次会话中保持上下文连续性。

## 📁 文件结构

```
├── memory_demo.py                    # 完整的记忆功能演示（包含Flow）
├── simple_flow/
│   ├── memory_demo_simple.py         # 简单的记忆功能演示
│   ├── content_crew.py               # 已集成记忆功能的ContentCrew
│   ├── main.py                       # 原始的Flow演示
│   └── config/                       # 配置文件
│       ├── agents.yaml
│       └── tasks.yaml
└── MEMORY_DEMO_README.md             # 本文件
```

## 🧠 记忆类型说明

CrewAI支持四种类型的记忆：

### 1. 短期记忆 (Short-term Memory)
- **用途**: 存储当前会话的上下文信息
- **存储方式**: RAG (Retrieval-Augmented Generation) 使用向量数据库
- **特点**: 
  - 支持语义搜索
  - 会话结束后可能清除
  - 适合存储临时上下文

### 2. 长期记忆 (Long-term Memory)
- **用途**: 跨会话持久化存储任务执行历史和质量评分
- **存储方式**: SQLite数据库
- **特点**:
  - 永久保存
  - 可用于学习和改进
  - 存储任务结果和性能指标

### 3. 实体记忆 (Entity Memory)
- **用途**: 存储重要实体信息和关系
- **存储方式**: 向量数据库 (RAG)
- **特点**:
  - 支持语义搜索
  - 存储实体关系
  - 便于知识图谱构建

### 4. 外部记忆 (External Memory)
- **用途**: 集成外部记忆系统（如Mem0）
- **存储方式**: 外部服务
- **特点**:
  - 可与其他应用共享
  - 支持云端同步
  - 扩展性强

## 🚀 快速开始

### 1. 运行简单演示

```bash
cd simple_flow
python memory_demo_simple.py
```

这个演示会：
- 创建带有记忆功能的ContentCrew
- 处理多个主题来展示记忆的累积效果
- 显示记忆文件的生成情况
- 提供清理选项

### 2. 运行完整演示

```bash
python memory_demo.py
```

这个演示包含：
- 完整的Flow工作流
- 记忆分析和提取
- 实体识别
- 详细的记忆报告

### 3. 运行原始Flow（现在带有记忆功能）

```bash
cd simple_flow
python main.py
```

现在的ContentCrew已经集成了记忆功能，每次运行都会：
- 保存任务执行历史
- 记住重要实体
- 维护上下文连续性

## 🔧 配置说明

### 基本配置

```python
from crewai import Crew
from crewai.memory import LongTermMemory, ShortTermMemory, EntityMemory

crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,  # 启用默认记忆配置
    verbose=True
)
```

### 自定义配置

```python
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
from crewai.memory.storage.rag_storage import RAGStorage

crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,
    # 自定义长期记忆
    long_term_memory=LongTermMemory(
        storage=LTMSQLiteStorage(db_path="./custom_memory.db")
    ),
    # 自定义短期记忆
    short_term_memory=ShortTermMemory(
        storage=RAGStorage(
            type="short_term",
            path="./custom_rag/"
        )
    ),
    # 自定义实体记忆
    entity_memory=EntityMemory(
        storage=RAGStorage(
            type="entities",
            path="./custom_entities/"
        )
    )
)
```

### 外部记忆配置（Mem0）

```python
from crewai.memory.external.external_memory import ExternalMemory

crew = Crew(
    agents=[...],
    tasks=[...],
    memory=True,
    external_memory=ExternalMemory(
        embedder_config={
            "provider": "mem0",
            "config": {
                "api_key": "your-mem0-api-key"
            }
        }
    )
)
```

## 📊 记忆功能优势

### 1. 个性化体验
- 记住用户偏好和习惯
- 根据历史交互调整响应
- 提供一致的用户体验

### 2. 上下文连续性
- 跨会话保持对话上下文
- 记住之前的决策和结果
- 避免重复询问相同信息

### 3. 学习能力
- 从每次交互中学习
- 改进任务执行质量
- 积累领域知识

### 4. 效率提升
- 避免重复处理相同信息
- 快速检索相关历史数据
- 智能推荐和建议

## 🗂️ 生成的文件

运行演示后，会生成以下文件：

```
├── content_crew_memory.db           # 长期记忆数据库
├── content_crew_rag/               # 短期记忆向量存储
│   ├── chroma.sqlite3
│   └── ...
├── content_crew_entities/          # 实体记忆向量存储
│   ├── chroma.sqlite3
│   └── ...
└── memory_demo.db                  # 完整演示的记忆数据库
```

## 🔍 记忆检索

### 搜索短期记忆

```python
# 在agent执行过程中自动进行
# 或手动搜索
results = crew._short_term_memory.search(
    query="人工智能相关内容",
    limit=5,
    score_threshold=0.7
)
```

### 查询长期记忆

```python
# 查询历史任务
results = crew._long_term_memory.search(
    task="内容创建任务",
    latest_n=10
)
```

### 搜索实体记忆

```python
# 搜索相关实体
results = crew._entity_memory.search(
    query="技术实体",
    limit=5
)
```

## 🛠️ 故障排除

### 常见问题

1. **记忆文件权限错误**
   ```bash
   chmod 755 ./content_crew_memory.db
   ```

2. **向量数据库初始化失败**
   - 确保有足够的磁盘空间
   - 检查目录写入权限

3. **API密钥配置**
   ```bash
   export OPENAI_API_KEY="your-api-key"
   # 或在代码中设置
   os.environ["OPENAI_API_KEY"] = "your-api-key"
   ```

### 清理记忆数据

```python
# 重置所有记忆
crew._long_term_memory.reset()
crew._short_term_memory.storage.reset()
crew._entity_memory.reset()
```

## 📚 进一步学习

- [CrewAI 官方文档](https://docs.crewai.com/)
- [记忆功能详细说明](https://docs.crewai.com/concepts/memory)
- [Flow工作流文档](https://docs.crewai.com/concepts/flows)

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

本项目遵循MIT许可证。
